Dependencies for Project 'LED_REGISTER', Target 'Target 1': (DO NOT MODIFY !)
CompilerVersion: 5060960::V5.06 update 7 (build 960)::.\arm5_compiler
F (.\Start\core_cm3.c)(0x68158274)(--gnu -c --cpu Cortex-M3 -D__EVAL -g -O0 --apcs=interwork --split_sections -I .\Start -I .\User

-IE:\KEIL4\KEIL4package\Keil\STM32F1xx_DFP\2.4.1\Device\Include

-D__UVISION_VERSION="538" -DSTM32F10X_HD

-o .\objects\core_cm3.o --omf_browse .\objects\core_cm3.crf --depend .\objects\core_cm3.d)
I (E:\KEIL4\ARM\arm5_compiler\include\stdint.h)(0x688B40E0)
F (.\Start\core_cm3.h)(0x68158276)()
F (.\Start\startup_stm32f10x_md.s)(0x68158506)(--cpu Cortex-M3 --pd "__EVAL SETA 1" -g --apcs=interwork 

-IE:\KEIL4\KEIL4package\Keil\STM32F1xx_DFP\2.4.1\Device\Include

--pd "__UVISION_VERSION SETA 538" --pd "STM32F10X_HD SETA 1"

--list .\listings\startup_stm32f10x_md.lst --xref -o .\objects\startup_stm32f10x_md.o --depend .\objects\startup_stm32f10x_md.d)
F (.\Start\stm32f10x.h)(0x68158418)()
F (.\Start\system_stm32f10x.c)(0x68158418)(--gnu -c --cpu Cortex-M3 -D__EVAL -g -O0 --apcs=interwork --split_sections -I .\Start -I .\User

-IE:\KEIL4\KEIL4package\Keil\STM32F1xx_DFP\2.4.1\Device\Include

-D__UVISION_VERSION="538" -DSTM32F10X_HD

-o .\objects\system_stm32f10x.o --omf_browse .\objects\system_stm32f10x.crf --depend .\objects\system_stm32f10x.d)
I (Start\stm32f10x.h)(0x68158418)
I (Start\core_cm3.h)(0x68158276)
I (E:\KEIL4\ARM\arm5_compiler\include\stdint.h)(0x688B40E0)
I (Start\system_stm32f10x.h)(0x68158418)
F (.\Start\system_stm32f10x.h)(0x68158418)()
F (.\User\main.c)(0x688C64AC)(--gnu -c --cpu Cortex-M3 -D__EVAL -g -O0 --apcs=interwork --split_sections -I .\Start -I .\User

-IE:\KEIL4\KEIL4package\Keil\STM32F1xx_DFP\2.4.1\Device\Include

-D__UVISION_VERSION="538" -DSTM32F10X_HD

-o .\objects\main.o --omf_browse .\objects\main.crf --depend .\objects\main.d)
I (.\Start\stm32f10x.h)(0x68158418)
I (.\Start\core_cm3.h)(0x68158276)
I (E:\KEIL4\ARM\arm5_compiler\include\stdint.h)(0x688B40E0)
I (.\Start\system_stm32f10x.h)(0x68158418)
